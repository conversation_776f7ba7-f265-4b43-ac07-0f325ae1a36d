"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Slider } from "@/components/ui/slider"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Upload, FileText, X, Check } from "lucide-react"
import { datasetApi, datagenEventApi } from "@/lib/api-client"

const steps = [
  { id: 1, title: "Application Description", description: "Describe your LLM application" },
  { id: 2, title: "Test Configuration", description: "Configure test parameters" },
  { id: 3, title: "Expert Collaboration", description: "Collaborate with domain experts" },
  { id: 4, title: "Generation Progress", description: "Monitor dataset generation" },
]

const testTypes = [
  { id: "qa", label: "Quality Assurance", checked: true },
  { id: "safety", label: "Safety Testing", checked: false },
  { id: "bias", label: "Bias Detection", checked: false },
  { id: "performance", label: "Performance Testing", checked: false },
  { id: "security", label: "Security Testing", checked: false },
]

const experts = [
  { id: "expert_003", name: "Dr. Sarah Chen", specialty: "Healthcare AI", rating: 4.9, collaborations: 12 },
  {
    id: "safety_expert",
    name: "Prof. Michael Rodriguez",
    specialty: "Safety & Security",
    rating: 4.8,
    collaborations: 8,
  },
  { id: "legal_expert", name: "Dr. Emily Watson", specialty: "Legal AI Ethics", rating: 4.7, collaborations: 15 },
  { id: "test_expert", name: "Alex Kim", specialty: "AI Testing", rating: 4.9, collaborations: 20 },
]

export function GenerateDataset() {
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({
    description: "It is a system that helps the police officer to understand the incident from citizen's reports.",
    domain: "legal",
    exampleInput: "On March 20, 2025, at 10:00 AM, at the Esplanade, I witnessed a concerning incident involving my friend Mei Xuan. We were enjoying the morning by the waterfront when a man approached us, presenting himself as a representative from a local charity. He claimed to be collecting donations and offered Mei Xuan a receipt for tax deductions. Intrigued, Mei Xuan began conversing with him and provided her contact information. Jia Hao, who was with us, noticed inconsistencies in the man's identification badge and his evasive behavior. As Mei Xuan began to question the legitimacy of his credentials, the man became defensive and hurriedly left the scene. Mei Xuan was left feeling deceived and uneasy. We reported the incident to nearby security personnel immediately.",
    testTypes: testTypes,
    complexity: [5],
    coverage: [70],
    datasetSize: [1000],
    // API-specific fields
    target_llm: "gpt-4o-mini",
    api_key: "",
    base_url: "",
    nickname: "",
    project_id: "",
  })
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([])
  const [selectedExperts, setSelectedExperts] = useState<string[]>([])
  const [progress, setProgress] = useState(0)
  const [isGenerating, setIsGenerating] = useState(false)
  const [generationError, setGenerationError] = useState<string | null>(null)
  const [generationSuccess, setGenerationSuccess] = useState(false)

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1)
      // Note: No longer calling startGeneration() here - it will be called on "Complete"
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    setUploadedFiles([...uploadedFiles, ...files])
  }

  const removeFile = (index: number) => {
    setUploadedFiles(uploadedFiles.filter((_, i) => i !== index))
  }

  const toggleExpert = (expertId: string) => {
    setSelectedExperts((prev) => (prev.includes(expertId) ? prev.filter((id) => id !== expertId) : [...prev, expertId]))
  }

  const startGeneration = async () => {
    setIsGenerating(true)
    setProgress(0)
    setGenerationError(null)
    setGenerationSuccess(false)

    try {
      console.log("🚀 Starting dataset generation and recording datagen event...")

      // Prepare datagen event data
      const eventData = {
        application_description: formData.description,
        domain: formData.domain,
        example_input: formData.exampleInput,
        test_types: formData.testTypes,
        complexity: formData.complexity[0],
        coverage: formData.coverage[0],
        dataset_size: formData.datasetSize[0],
        selected_experts: selectedExperts,
        uploaded_files: uploadedFiles.map(file => file.name),
        user_session_id: `session-${Date.now()}`, // Simple session ID for demo
        project_id: formData.project_id || undefined
      }

      // Prepare dataset generation API request data matching base.yml structure
      const requestData = {
        // Core fields matching base.yml
        description: formData.description || undefined,
        examples: formData.exampleInput ? [formData.exampleInput] : undefined,
        task: "Content",
        llm: {
          provider: "openai",
          api_key: formData.api_key || undefined,
          base_url: formData.base_url || undefined,
          model_kwargs: {
            model: formData.target_llm
          },
          sampling_params: {
            temperature: 0.7,
            max_tokens: 2048,
            top_p: 0.9,
            n: Math.floor(formData.datasetSize[0] / 100) // Convert dataset size to samples
          }
        },
        // Additional fields for job tracking
        nickname: formData.nickname || undefined,
        project_id: formData.project_id || undefined,
      }

      console.log("📝 Recording datagen event with data:", eventData)
      console.log("🚀 Starting dataset generation with data:", requestData)

      // Call both APIs simultaneously
      const [eventResponse, datasetResponse] = await Promise.all([
        datagenEventApi.create(eventData),
        datasetApi.generate(requestData)
      ])

      // Check for errors in either response
      if (eventResponse.error) {
        console.warn("⚠️ Warning: Failed to record datagen event:", eventResponse.error)
        // Continue anyway - don't block dataset generation
      } else {
        console.log("✅ Datagen event recorded successfully:", eventResponse.data)
      }

      if (datasetResponse.error) {
        throw new Error(datasetResponse.error)
      }

      console.log("✅ Dataset generation started:", datasetResponse.data)

      // Simulate progress since the API runs in background
      const interval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 100) {
            clearInterval(interval)
            setIsGenerating(false)
            setGenerationSuccess(true)
            return 100
          }
          return prev + Math.random() * 10 + 5
        })
      }, 800)

    } catch (error) {
      console.error("❌ Dataset generation failed:", error)
      setGenerationError(error instanceof Error ? error.message : "Unknown error occurred")
      setIsGenerating(false)
      setProgress(0)
    }
  }

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="description">Application Description</Label>
              <Textarea
                id="description"
                placeholder="Describe your LLM application, its purpose, and key functionality..."
                rows={6}
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="domain">Application Domain</Label>
              <Select value={formData.domain} onValueChange={(value) => setFormData({ ...formData, domain: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select domain..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="healthcare">Healthcare</SelectItem>
                  <SelectItem value="finance">Finance</SelectItem>
                  <SelectItem value="customer-service">Customer Service</SelectItem>
                  <SelectItem value="education">Education</SelectItem>
                  <SelectItem value="legal">Legal</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="example">Example Input</Label>
              <Textarea
                id="example"
                placeholder="Provide example inputs your application receives..."
                rows={3}
                value={formData.exampleInput}
                onChange={(e) => setFormData({ ...formData, exampleInput: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label>Reference Documents</Label>
              <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                <Upload className="mx-auto h-12 w-12 text-muted-foreground/50" />
                <div className="mt-2">
                  <Label htmlFor="file-upload" className="cursor-pointer">
                    <span className="text-sm text-muted-foreground">Drag and drop files here or click to upload</span>
                    <Input id="file-upload" type="file" multiple className="hidden" onChange={handleFileUpload} />
                  </Label>
                </div>
              </div>
              {uploadedFiles.length > 0 && (
                <div className="space-y-2">
                  {uploadedFiles.map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        <span className="text-sm">{file.name}</span>
                        <span className="text-xs text-muted-foreground">({(file.size / 1024).toFixed(1)} KB)</span>
                      </div>
                      <Button variant="ghost" size="sm" onClick={() => removeFile(index)}>
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )

      case 2:
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-6">
              <div>
                <h4 className="font-medium mb-4">Test Types</h4>
                <div className="space-y-3">
                  {formData.testTypes.map((type) => (
                    <div key={type.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={type.id}
                        checked={type.checked}
                        onCheckedChange={(checked) => {
                          const updatedTypes = formData.testTypes.map((t) =>
                            t.id === type.id ? { ...t, checked: !!checked } : t,
                          )
                          setFormData({ ...formData, testTypes: updatedTypes })
                        }}
                      />
                      <Label htmlFor={type.id}>{type.label}</Label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <div className="space-y-6">
              <div>
                <h4 className="font-medium mb-4">Parameters</h4>
                <div className="space-y-6">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <Label>Test Complexity</Label>
                      <span className="text-sm text-muted-foreground">{formData.complexity[0]}</span>
                    </div>
                    <Slider
                      value={formData.complexity}
                      onValueChange={(value) => setFormData({ ...formData, complexity: value })}
                      max={10}
                      min={1}
                      step={1}
                    />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <Label>Coverage</Label>
                      <span className="text-sm text-muted-foreground">{formData.coverage[0]}%</span>
                    </div>
                    <Slider
                      value={formData.coverage}
                      onValueChange={(value) => setFormData({ ...formData, coverage: value })}
                      max={100}
                      min={10}
                      step={5}
                    />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <Label>Dataset Size</Label>
                      <span className="text-sm text-muted-foreground">{formData.datasetSize[0]}</span>
                    </div>
                    <Slider
                      value={formData.datasetSize}
                      onValueChange={(value) => setFormData({ ...formData, datasetSize: value })}
                      max={5000}
                      min={100}
                      step={100}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="target_llm">Target LLM</Label>
                    <Select value={formData.target_llm} onValueChange={(value) => setFormData({ ...formData, target_llm: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select target LLM..." />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                        <SelectItem value="gpt-4">GPT-4</SelectItem>
                        <SelectItem value="gpt-4o-mini">GPT-4o Mini</SelectItem>
                        <SelectItem value="claude-3-haiku">Claude 3 Haiku</SelectItem>
                        <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="api_key">API Key (Optional)</Label>
                    <Input
                      id="api_key"
                      type="password"
                      placeholder="Enter your API key..."
                      value={formData.api_key}
                      onChange={(e) => setFormData({ ...formData, api_key: e.target.value })}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <div>
              <h4 className="font-medium mb-4">Select Domain Experts</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {experts.map((expert) => (
                  <Card
                    key={expert.id}
                    className={`cursor-pointer transition-colors ${
                      selectedExperts.includes(expert.id) ? "ring-2 ring-primary" : ""
                    }`}
                    onClick={() => toggleExpert(expert.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarFallback>
                            {expert.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="font-medium">{expert.name}</div>
                          <div className="text-sm text-muted-foreground">{expert.specialty}</div>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <span>⭐ {expert.rating}</span>
                            <span>({expert.collaborations} collaborations)</span>
                          </div>
                        </div>
                        {selectedExperts.includes(expert.id) && <Check className="h-5 w-5 text-primary" />}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h4 className="font-medium">Generation Progress</h4>
                <span className="text-sm text-muted-foreground">{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Generation Logs</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-1 font-mono text-sm">
                  {generationError ? (
                    <div className="text-red-600">❌ Error: {generationError}</div>
                  ) : (
                    <>
                      <div className="text-muted-foreground">🚀 Starting dataset generation...</div>
                      <div className="text-muted-foreground">
                        📝 Processing application description for domain: {formData.domain}
                      </div>
                      <div className="text-muted-foreground">🎯 Target LLM: {formData.target_llm}</div>
                      {progress > 20 && <div className="text-muted-foreground">🔄 Generating adversarial prompts...</div>}
                      {progress > 50 && <div className="text-muted-foreground">🧪 Running safety tests...</div>}
                      {progress > 80 && <div className="text-muted-foreground">📊 Analyzing results...</div>}
                      {generationSuccess && <div className="text-green-600">✅ Dataset generation completed successfully!</div>}
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Steps */}
      <div className="flex items-center justify-center">
        <div className="flex items-center space-x-8">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className="flex flex-col items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    currentStep === step.id
                      ? "bg-primary text-primary-foreground"
                      : currentStep > step.id
                        ? "bg-green-500 text-white"
                        : "bg-muted text-muted-foreground"
                  }`}
                >
                  {currentStep > step.id ? <Check className="h-4 w-4" /> : step.id}
                </div>
                <div className="mt-2 text-center">
                  <div className="text-sm font-medium">{step.title}</div>
                  <div className="text-xs text-muted-foreground">{step.description}</div>
                </div>
              </div>
              {index < steps.length - 1 && <div className="w-16 h-px bg-muted mx-4" />}
            </div>
          ))}
        </div>
      </div>

      {/* Content */}
      <Card>
        <CardContent className="p-6">{renderStep()}</CardContent>
      </Card>

      {/* Actions */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={handlePrevious} disabled={currentStep === 1}>
          Previous
        </Button>
        <Button
          onClick={currentStep === 4 ? startGeneration : handleNext}
          disabled={currentStep === 4 && isGenerating}
        >
          {currentStep === 4 ? (isGenerating ? "Generating..." : "Complete") : "Next"}
        </Button>
      </div>
    </div>
  )
}
