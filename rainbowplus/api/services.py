from typing import Dict, List, Tuple, Optional, Any

from rainbowplus.rainbowplus import (
    ConfigurationLoader,
    parse_arguments,
    run_rainbowplus,
)
from rainbowplus.scores import BleuScoreNLTK, create_openai_guard
from rainbowplus.utils import initialize_language_models

from .models import PointResponse, RainbowPlusResponse, ProjectCreateRequest, ProjectResponse, ProjectListResponse, ProjectDashboardResponse, RecentProjectsResponse, DatagenEventListResponse, DatagenEventDetailResponse, DatasetGenerationRequest, DatasetGenerationResponse
from abc import ABC, abstractmethod
from rainbowplus.api.models import RainbowAnalysisJob, Project
from rainbowplus.api.tasks import run_checkpointed_task
from rainbowplus.api.database import get_db_session
from rainbowplus.api.logic import _process_results_logic
# Simple strategy interface
class AnalysisStrategy(ABC):
    @abstractmethod
    def run_analysis(
        self,
        prompts: List[str],
        target_llm: str,
        num_samples: int,
        num_mutations: int,
        max_iters: int,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        nickname: Optional[str] = None
    ) -> Dict[str, Any]:
        pass

class RainbowPlusService(AnalysisStrategy):
    @staticmethod
    def run_analysis(
        prompts: List[str],
        target_llm: str,
        num_samples: int,
        num_mutations: int,
        max_iters: int,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        nickname: Optional[str] = None,
        job_id: Optional[str] = None,
        project_id: Optional[str] = None,
        db_session_factory=None,
    ) -> RainbowPlusResponse:
        # Parse and update arguments
        args = parse_arguments()
        args.num_samples = num_samples
        args.num_mutations = num_mutations
        args.max_iters = max_iters
        args.api_key = api_key
        args.base_url = base_url
        # Load and update configuration
        config = ConfigurationLoader.load(args.config_file)
        config.target_llm.model_kwargs["model"] = target_llm
        config.target_llm.api_key = api_key
        config.target_llm.base_url = base_url
        # Initialize components
        llms = initialize_language_models(config)
        fitness_fn = create_openai_guard(config.fitness_llm)
        # if config.fitness_llm.type_ == "azure_openai":
        #     fitness_fn = AzureOpenAIGuard(config.fitness_llm)
        # else:
        #     fitness_fn = LlamaGuard(config.fitness_llm.model_kwargs)
        similarity_fn = BleuScoreNLTK()

        # Run analysis
        adv_prompts, responses, scores = run_rainbowplus(
            args, config, prompts, llms, fitness_fn, similarity_fn, custom_prompts=None
        )

        # Process results
        points = RainbowPlusService._process_results(adv_prompts, responses, scores)

        # Save results to dataset table if job_id and project_id are provided
        if job_id and project_id:
            dataset_service = DatasetService(db_session_factory=db_session_factory)
            for point in points:
                dataset_service.save_dataset_entry(
                    job_id=job_id,
                    project_id=project_id,
                    status="generated",
                    content={"adv_prompt": point.adv_prompt, "response": point.response},
                    label=point.descriptor,
                )

        return RainbowPlusResponse(points=points)

    @staticmethod
    def _process_results(
        adv_prompts: Dict[str, List[str]],
        responses: Dict[str, List[str]],
        scores: Dict[str, List[float]],
    ) -> List[PointResponse]:
        points = []
        keys = list(adv_prompts.keys())
        for key in keys:
            length = len(adv_prompts.get(key))
            for idx in range(length):
                point = PointResponse(
                    adv_prompt=adv_prompts.get(key)[idx],
                    response=responses.get(key)[idx],
                    score=scores.get(key)[idx],
                    descriptor=str(key),
                )
                points.append(point)
        return points
class CheckpointedAsyncStrategy(AnalysisStrategy):
    def run_analysis(
        self,
        prompts: List[str],
        target_llm: str,
        num_samples: int,
        num_mutations: int,
        max_iters: int,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        nickname: Optional[str] = None,
        project_id: Optional[str] = None
    ) -> Dict[str, Any]:
        
        
        # Create job record
        with get_db_session() as session:
            job = RainbowAnalysisJob(
                prompts=prompts,
                target_llm=target_llm,
                num_samples=num_samples,
                num_mutations=num_mutations,
                max_iters=max_iters,
                api_key=api_key,
                base_url=base_url,
                nickname=nickname,
                project_id=project_id,
                status='pending'
            )
            session.add(job)
            session.flush()
            job_id = job.id
        
        # Start task
        # task = run_checkpointed_task.delay(job_id,prompts,target_llm,num_samples,num_mutations,max_iters,api_key,base_url)
        task = None
        try:
            task = run_checkpointed_task(
                job_id,
                prompts,
                target_llm,
                num_samples,
                num_mutations,
                max_iters,
                api_key,
                base_url
            )
        except Exception as e:
            print(f"Error running task: {e}")
        return {
            "job_id": job_id,
            # "task_id": task["task_id"],
            "status": "processing"
        }


class ProjectService:
    """Service for managing projects with constructor-based dependency injection."""

    def __init__(self, db_session_factory=None):
        """
        Initialize ProjectService with optional dependency injection.

        Args:
            db_session_factory: Optional database session factory for testing
        """
        self.db_session_factory = db_session_factory or get_db_session

    def create_project(self, request: ProjectCreateRequest) -> ProjectResponse:
        """
        Create a new project.

        Args:
            request: Project creation request data

        Returns:
            ProjectResponse: Created project data

        Raises:
            ValueError: If project name already exists
        """
        with self.db_session_factory() as session:
            # Check if project name already exists
            existing_project = session.query(Project).filter(Project.name == request.name).first()
            if existing_project:
                raise ValueError(f"Project with name '{request.name}' already exists")

            # Create new project
            project = Project(
                name=request.name,
                description=request.description,
                domain=request.domain
            )

            session.add(project)
            session.flush()  # Get the ID

            # Convert to response model
            return ProjectResponse(
                id=str(project.id),
                name=project.name,
                description=project.description,
                domain=project.domain,
                created_at=project.created_at.isoformat(),
                updated_at=project.updated_at.isoformat()
            )

    def get_project(self, project_id: str) -> Optional[ProjectResponse]:
        """
        Get a project by ID.

        Args:
            project_id: UUID string of the project

        Returns:
            ProjectResponse or None if not found
        """
        with self.db_session_factory() as session:
            project = session.query(Project).filter(Project.id == project_id).first()
            if not project:
                return None

            return ProjectResponse(
                id=str(project.id),
                name=project.name,
                description=project.description,
                domain=project.domain,
                created_at=project.created_at.isoformat(),
                updated_at=project.updated_at.isoformat()
            )

    def list_projects(self) -> 'ProjectDashboardListResponse':
        """
        List all projects with dashboard statistics.

        Returns:
            ProjectDashboardListResponse: List of all projects with statistics
        """
        with self.db_session_factory() as session:
            projects = session.query(Project).order_by(Project.created_at.desc()).all()

            # Convert ORM objects to ProjectDashboardResponse objects (with statistics)
            project_responses = []
            for project in projects:
                dashboard_data = self._compute_project_statistics(session, project)
                project_responses.append(dashboard_data)

            from .models import ProjectDashboardListResponse
            return ProjectDashboardListResponse(projects=project_responses)

    def update_project(self, project_id: str, request: ProjectCreateRequest) -> Optional[ProjectResponse]:
        """
        Update an existing project.

        Args:
            project_id: UUID string of the project
            request: Updated project data

        Returns:
            ProjectResponse or None if not found

        Raises:
            ValueError: If new project name already exists (and is different from current)
        """
        with self.db_session_factory() as session:
            project = session.query(Project).filter(Project.id == project_id).first()
            if not project:
                return None

            # Check if new name conflicts with existing project (excluding current project)
            if request.name != project.name:
                existing_project = session.query(Project).filter(
                    Project.name == request.name,
                    Project.id != project_id
                ).first()
                if existing_project:
                    raise ValueError(f"Project with name '{request.name}' already exists")

            # Update project fields
            project.name = request.name
            project.description = request.description
            project.domain = request.domain

            session.flush()

            return ProjectResponse(
                id=str(project.id),
                name=project.name,
                description=project.description,
                domain=project.domain,
                created_at=project.created_at.isoformat(),
                updated_at=project.updated_at.isoformat()
            )

    def delete_project(self, project_id: str) -> bool:
        """
        Delete a project.

        Args:
            project_id: UUID string of the project

        Returns:
            bool: True if deleted, False if not found

        Note:
            This will set project_id to NULL for any associated jobs
        """
        with self.db_session_factory() as session:
            project = session.query(Project).filter(Project.id == project_id).first()
            if not project:
                return False

            # Update associated jobs to remove project reference
            session.query(RainbowAnalysisJob).filter(
                RainbowAnalysisJob.project_id == project_id
            ).update({RainbowAnalysisJob.project_id: None})

            # Delete the project
            session.delete(project)

            return True

    def get_recent_projects(self, limit: int = 10) -> RecentProjectsResponse:
        """
        Get recent projects with computed dashboard statistics.

        Args:
            limit: Maximum number of projects to return (default: 10)

        Returns:
            RecentProjectsResponse: List of recent projects with statistics
        """
        with self.db_session_factory() as session:
            # Get recent projects ordered by updated_at
            projects = session.query(Project).order_by(Project.updated_at.desc()).limit(limit).all()

            project_responses = []
            for project in projects:
                dashboard_data = self._compute_project_statistics(session, project)
                project_responses.append(dashboard_data)

            return RecentProjectsResponse(projects=project_responses)

    def _compute_project_statistics(self, session, project: Project) -> ProjectDashboardResponse:
        """
        Compute dashboard statistics for a project based on its associated jobs.

        Args:
            session: Database session
            project: Project instance

        Returns:
            ProjectDashboardResponse: Project with computed statistics
        """
        # Get all jobs for this project
        jobs = session.query(RainbowAnalysisJob).filter(
            RainbowAnalysisJob.project_id == project.id
        ).all()

        # Compute statistics
        total_jobs = len(jobs)
        completed_jobs = len([job for job in jobs if job.status == 'completed'])
        pending_jobs = len([job for job in jobs if job.status == 'pending'])

        # Count total datasets generated across all jobs
        datasets_generated = 0
        for job in jobs:
            if job.results and isinstance(job.results, dict) and 'points' in job.results:
                datasets_generated += len(job.results['points'])

        # Determine overall project status
        if total_jobs == 0:
            status = "pending"
        elif completed_jobs == total_jobs:
            status = "completed"
        elif pending_jobs > 0:
            status = "active"
        else:
            status = "active"

        # Calculate average risk score from completed jobs
        risk_scores = []
        for job in jobs:
            if job.status == 'completed' and job.results and isinstance(job.results, dict) and 'points' in job.results:
                for point in job.results['points']:
                    if 'score' in point:
                        risk_scores.append(point['score'])

        avg_risk_score = sum(risk_scores) / len(risk_scores) if risk_scores else 0.0

        # Compute progress percentage
        if total_jobs == 0:
            progress = 0
        else:
            progress = int((completed_jobs / total_jobs) * 100)

        return ProjectDashboardResponse(
            id=str(project.id),
            name=project.name,
            description=project.description,
            domain=project.domain,
            status=status,
            datasetsGenerated=datasets_generated,
            testsRun=completed_jobs,
            riskScore=round(avg_risk_score, 2),
            progress=progress,
            created_at=project.created_at.isoformat(),
            updated_at=project.updated_at.isoformat()
        )


class DatasetService:
    """Service for managing datasets with constructor-based dependency injection."""

    def __init__(self, db_session_factory=None):
        """
        Initialize DatasetService with optional dependency injection.

        Args:
            db_session_factory: Optional database session factory for testing
        """
        self.db_session_factory = db_session_factory or get_db_session

    def save_dataset_entry(self, job_id: str, project_id: str, status: str, content: dict, label: str) -> None:
        """
        Save a dataset entry to the database.

        Args:
            job_id: UUID string of the associated analysis job
            project_id: UUID string of the associated project (optional)
            status: Status string for the dataset entry
            content: JSON content of the dataset entry
            label: Label string for the dataset entry

        Returns:
            None
        """
        from .models import Dataset

        with self.db_session_factory() as session:
            dataset_entry = Dataset(
                job_id=job_id,
                project_id=project_id,
                status=status,
                content=content,
                label=label
            )
            session.add(dataset_entry)
            session.flush()


class DatagenEventService:
    """Service for managing dataset generation events with constructor-based dependency injection."""

    def __init__(self, db_session_factory=None):
        """
        Initialize DatagenEventService with optional dependency injection.

        Args:
            db_session_factory: Optional database session factory for testing
        """
        self.db_session_factory = db_session_factory or get_db_session

    def _get_or_create_default_project(self, session, project_id: str = None) -> str:
        """
        Get existing project or create a default project if none provided.

        Args:
            session: Database session
            project_id: Optional project ID

        Returns:
            str: Project ID (existing or newly created)
        """
        from datetime import datetime, timezone
        from .models import Project

        if project_id and project_id.strip():
            return project_id.strip()

        # Create a default blank project
        default_project = Project(
            name=f"Dataset Generation {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')}",
            description="Auto-generated project for dataset generation without specified project",
            domain="General"
        )
        session.add(default_project)
        session.flush()  # Get the ID
        project_id = str(default_project.id)
        print(f"✅ Created default project for dataset generation: {project_id}")
        return project_id

    def create_datagen_event(self, request: 'DatagenEventRequest') -> 'DatagenEventResponse':
        """
        Create a new datagen event record from form data at step 3 completion.

        Args:
            request: Form data from dataset generation workflow

        Returns:
            DatagenEventResponse: Response with event ID and status
        """
        import uuid
        from datetime import datetime, timezone
        from .models import DatagenEvent

        # Create a new event ID
        event_id = str(uuid.uuid4())

        with self.db_session_factory() as session:
            # Extract test types data
            test_types_data = None
            if request.test_types:
                test_types_data = [test_type.dict() for test_type in request.test_types]

            # Handle project_id - create default project if none provided
            project_id = self._get_or_create_default_project(session, request.project_id)

            # Create event record
            event = DatagenEvent(
                id=event_id,
                application_description=request.application_description,
                domain=request.domain,
                example_input=request.example_input,
                test_types=test_types_data,
                complexity=request.complexity,
                coverage=request.coverage,
                dataset_size=request.dataset_size,
                selected_experts=request.selected_experts,
                uploaded_files=request.uploaded_files,
                user_session_id=request.user_session_id,
                project_id=project_id,
                step_completed=4,  # Step 4 (Complete) - when actual generation starts
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )

            session.add(event)
            session.flush()

        from .models import DatagenEventResponse
        return DatagenEventResponse(
            event_id=event_id,
            status='success',
            message='Dataset generation event recorded successfully'
        )

    def get_datagen_event(self, event_id: str) -> Optional['DatagenEventResponse']:
        """
        Get a datagen event by ID.

        Args:
            event_id: UUID string of the event

        Returns:
            DatagenEventResponse or None if not found
        """
        with self.db_session_factory() as session:
            from .models import DatagenEvent
            event = session.query(DatagenEvent).filter(DatagenEvent.id == event_id).first()

            if not event:
                return None

            from .models import DatagenEventResponse
            return DatagenEventResponse(
                event_id=str(event.id),
                status='success',
                message='Dataset generation event retrieved successfully'
            )

    def list_datagen_events(
        self,
        expert_id: str = None,
        project_id: str = None,
        domain: str = None,
        limit: int = 100,
        offset: int = 0
    ) -> 'DatagenEventListResponse':
        """
        List datagen events with optional filtering for expert review.

        Args:
            expert_id: Filter by expert ID (events where this expert is assigned)
            project_id: Filter by project ID
            domain: Filter by application domain
            limit: Maximum number of events to return
            offset: Number of events to skip

        Returns:
            DatagenEventListResponse: List of datagen events with metadata
        """
        from .models import DatagenEvent, Project, DatagenEventListResponse, DatagenEventDetailResponse
        from sqlalchemy.orm import joinedload

        with self.db_session_factory() as session:
            # Build query with optional joins
            query = session.query(DatagenEvent).options(joinedload(DatagenEvent.project))

            # Apply filters
            if expert_id:
                # Filter by expert ID in the selected_experts JSON array using PostgreSQL JSON operators
                from sqlalchemy import text
                query = query.filter(text("selected_experts @> :expert_id")).params(expert_id=f'["{expert_id}"]')

            if project_id:
                query = query.filter(DatagenEvent.project_id == project_id)

            if domain:
                query = query.filter(DatagenEvent.domain == domain)

            # Get total count before applying limit/offset
            total_count = query.count()

            # Apply ordering, limit, and offset
            events = query.order_by(DatagenEvent.created_at.desc())\
                          .offset(offset)\
                          .limit(limit)\
                          .all()

            # Convert to response objects
            event_responses = []
            for event in events:
                event_response = DatagenEventDetailResponse(
                    id=str(event.id),
                    application_description=event.application_description,
                    domain=event.domain,
                    example_input=event.example_input,
                    test_types=event.test_types,
                    complexity=event.complexity,
                    coverage=event.coverage,
                    dataset_size=event.dataset_size,
                    selected_experts=event.selected_experts,
                    uploaded_files=event.uploaded_files,
                    user_session_id=event.user_session_id,
                    step_completed=event.step_completed,
                    project_id=str(event.project_id) if event.project_id else None,
                    project_name=event.project.name if event.project else None,
                    created_at=event.created_at.isoformat() if event.created_at else None,
                    updated_at=event.updated_at.isoformat() if event.updated_at else None
                )
                event_responses.append(event_response)

            return DatagenEventListResponse(
                events=event_responses,
                total_count=total_count
            )


class DataPreparationService:
    """Service for data preparation pipeline with constructor-based dependency injection."""

    def __init__(self, db_session_factory=None):
        """
        Initialize DataPreparationService with optional dependency injection.

        Args:
            db_session_factory: Optional database session factory for testing
        """
        self.db_session_factory = db_session_factory or get_db_session

    def _get_or_create_default_project(self, session, project_id: str = None) -> str:
        """
        Get existing project or create a default project if none provided.

        Args:
            session: Database session
            project_id: Optional project ID

        Returns:
            str: Project ID (existing or newly created)
        """
        from datetime import datetime, timezone
        from .models import Project

        if project_id and project_id.strip():
            return project_id.strip()

        # Create a default blank project
        default_project = Project(
            name=f"Dataset Generation {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')}",
            description="Auto-generated project for dataset generation without specified project",
            domain="General"
        )
        session.add(default_project)
        session.flush()  # Get the ID
        project_id = str(default_project.id)
        print(f"✅ Created default project for dataset generation: {project_id}")
        return project_id

    def generate_dataset(self, request: 'DatasetGenerationRequest') -> 'DatasetGenerationResponse':
        """
        Generate a dataset using the data preparation pipeline with dynamic configuration.

        Args:
            request: Dataset generation request with client form data

        Returns:
            DatasetGenerationResponse: Response with job information

        Raises:
            ValueError: If required fields are missing or invalid
        """
        import uuid
        from datetime import datetime, timezone
        from .models import RainbowAnalysisJob

        # Create a new job record
        job_id = str(uuid.uuid4())

        with self.db_session_factory() as session:
            # Handle project_id - create default blank project if none provided
            project_id = self._get_or_create_default_project(session, request.project_id)

            # Extract LLM config for job record with safe access
            llm_config = request.llm

            # Safely extract values with fallbacks
            target_llm = 'gpt-4o-mini'  # Default
            num_samples = 12  # Default
            api_key = None
            base_url = None

            if llm_config:
                if hasattr(llm_config, 'model_kwargs') and llm_config.model_kwargs:
                    target_llm = llm_config.model_kwargs.get('model', 'gpt-4o-mini')
                if hasattr(llm_config, 'sampling_params') and llm_config.sampling_params:
                    num_samples = llm_config.sampling_params.get('n', 12)
                if hasattr(llm_config, 'api_key'):
                    api_key = llm_config.api_key
                if hasattr(llm_config, 'base_url'):
                    base_url = llm_config.base_url

            # Create job record
            job = RainbowAnalysisJob(
                id=job_id,
                prompts=request.examples or [],  # Store examples as prompts
                target_llm=target_llm,
                num_samples=num_samples,
                num_mutations=5,  # Default value
                max_iters=1,  # Default value
                api_key=api_key,
                base_url=base_url,
                nickname=request.nickname,
                project_id=project_id,  # Use the project_id (either provided or newly created)
                status='pending',
                created_at=datetime.now(timezone.utc)
            )
            session.add(job)
            session.flush()

        # Run data preparation pipeline asynchronously
        self._run_data_preparation_async(job_id, request)

        from .models import DatasetGenerationResponse
        return DatasetGenerationResponse(
            job_id=job_id,
            status='pending',
            message='Dataset generation started successfully'
        )

    def _run_data_preparation_async(self, job_id: str, request: 'DatasetGenerationRequest'):
        """
        Run the data preparation pipeline asynchronously.

        Args:
            job_id: The job ID to update with results
            request: The dataset generation request
        """
        import threading

        def run_pipeline():
            try:
                self._execute_data_preparation_pipeline(job_id, request)
            except Exception as e:
                # Update job status to failed
                with self.db_session_factory() as session:
                    from .models import RainbowAnalysisJob
                    job = session.query(RainbowAnalysisJob).filter(RainbowAnalysisJob.id == job_id).first()
                    if job:
                        job.status = 'failed'
                        job.results = {'error': str(e)}
                        session.add(job)

        # Start the pipeline in a background thread
        thread = threading.Thread(target=run_pipeline)
        thread.daemon = True
        thread.start()

    def _execute_data_preparation_pipeline(self, job_id: str, request: 'DatasetGenerationRequest'):
        """
        Execute the data preparation pipeline with dynamic configuration.

        Args:
            job_id: The job ID to update with results
            request: The dataset generation request
        """
        from data_preparation.core.config.base import Config, LLMConfig
        from data_preparation.core.pipeline import Pipeline
        import os

        # Update job status to running
        with self.db_session_factory() as session:
            from .models import RainbowAnalysisJob
            job = session.query(RainbowAnalysisJob).filter(RainbowAnalysisJob.id == job_id).first()
            if job:
                job.status = 'running'
                session.add(job)

        # Create dynamic configuration
        config = self._create_dynamic_config(request)

        # Create and run pipeline
        pipeline = Pipeline(config, self.db_session_factory)
        samples = pipeline.generate(job_id=job_id)

        return samples

    def _create_dynamic_config(self, request: 'DatasetGenerationRequest') -> 'Config':
        """
        Create a dynamic configuration from the client request, using base.yml as fallback.

        Args:
            request: The dataset generation request

        Returns:
            Config: Dynamic configuration for the pipeline
        """
        from data_preparation.core.config.base import Config, LLMConfig, ConfigLoader
        import tempfile
        import os

        # Load base configuration as fallback
        base_config_path = "data_preparation/recipes/base.yml"
        base_config = ConfigLoader.load(base_config_path)

        # Create temporary files for dynamic content
        temp_dir = tempfile.mkdtemp()

        # Create description file from client's description or fallback to base.yml
        if request.description:
            description_content = request.description
            description_path = os.path.join(temp_dir, "description.txt")
            with open(description_path, 'w') as f:
                f.write(description_content)
        else:
            # Use base.yml description - but create temp file with actual content
            description_content = self._get_description_content()
            description_path = os.path.join(temp_dir, "description.txt")
            with open(description_path, 'w') as f:
                f.write(description_content)

        # Create examples file from client's examples or fallback to base.yml
        if request.examples:
            example_paths = []
            for i, example in enumerate(request.examples):
                example_path = os.path.join(temp_dir, f"example_{i}.txt")
                with open(example_path, 'w') as f:
                    f.write(example)
                example_paths.append(example_path)
        else:
            # Use base.yml examples
            example_paths = base_config.examples

        # Create documents file from client's documents or fallback to base.yml
        if request.documents:
            document_paths = []
            for i, document in enumerate(request.documents):
                document_path = os.path.join(temp_dir, f"document_{i}.txt")
                with open(document_path, 'w') as f:
                    f.write(document)
                document_paths.append(document_path)
        else:
            # Use base.yml documents
            document_paths = base_config.documents

        # Create LLM configuration from client's LLM config or fallback to base.yml
        if request.llm:
            llm_config = LLMConfig(
                provider=request.llm.provider or base_config.llm.provider,
                api_key=request.llm.api_key or base_config.llm.api_key,
                base_url=request.llm.base_url or base_config.llm.base_url,
                model=request.llm.model or base_config.llm.model,
                sampling_params=request.llm.sampling_params or base_config.llm.sampling_params
            )
        else:
            llm_config = base_config.llm

        # Create dynamic configuration
        config = Config(
            description=description_path,
            examples=example_paths,
            documents=document_paths,
            task=request.task or base_config.task,
            llm=llm_config
        )

        return config

    def _get_description_content(self) -> str:
        """Get the description content from the base configuration file."""
        # This reads the actual content from the description file referenced in base.yml
        from data_preparation.core.utils import load_txt
        base_config_path = "data_preparation/recipes/base.yml"
        from data_preparation.core.config.base import ConfigLoader
        base_config = ConfigLoader.load(base_config_path)

        # Load the actual description content
        import os
        base_path = os.path.dirname(os.path.abspath(base_config_path))
        description_path = os.path.join(base_path, base_config.description)
        return load_txt(description_path)


class DatasetSampleService:
    """Service for managing individual dataset samples with constructor-based dependency injection."""

    def __init__(self, db_session_factory=None):
        """
        Initialize DatasetSampleService with optional dependency injection.

        Args:
            db_session_factory: Optional database session factory for testing
        """
        self.db_session_factory = db_session_factory or get_db_session

    def list_dataset_samples(
        self,
        project_id: str = None,
        datagen_event_id: str = None,
        generated_dataset_id: str = None,
        label: str = None,
        limit: int = 100,
        offset: int = 0
    ) -> 'DatasetSamplesListResponse':
        """
        List dataset samples with optional filtering.

        Args:
            project_id: Filter by project ID
            datagen_event_id: Filter by datagen event ID
            generated_dataset_id: Filter by generated dataset ID
            label: Filter by sample label
            limit: Maximum number of samples to return
            offset: Number of samples to skip

        Returns:
            DatasetSamplesListResponse: List of dataset samples with metadata
        """
        from .models import DatasetSample, DatasetSamplesListResponse, DatasetSampleResponse

        with self.db_session_factory() as session:
            # Build query with filters
            query = session.query(DatasetSample)

            if project_id:
                query = query.filter(DatasetSample.project_id == project_id)
            if datagen_event_id:
                query = query.filter(DatasetSample.datagen_event_id == datagen_event_id)
            if generated_dataset_id:
                query = query.filter(DatasetSample.generated_dataset_id == generated_dataset_id)
            if label:
                query = query.filter(DatasetSample.label == label)

            # Get total count before applying limit/offset
            total_count = query.count()

            # Apply ordering, limit, and offset
            samples = query.order_by(DatasetSample.created_at.desc())\
                          .offset(offset)\
                          .limit(limit)\
                          .all()

            # Convert to response models
            sample_responses = []
            for sample in samples:
                sample_response = DatasetSampleResponse(
                    id=str(sample.id),
                    generated_dataset_id=str(sample.generated_dataset_id),
                    datagen_event_id=str(sample.datagen_event_id) if sample.datagen_event_id else None,
                    project_id=str(sample.project_id) if sample.project_id else None,
                    job_id=str(sample.job_id) if sample.job_id else None,
                    sample_type=sample.sample_type,
                    content=sample.content,
                    label=sample.label,
                    sample_group=sample.sample_group,
                    sample_index=sample.sample_index,
                    created_at=sample.created_at.isoformat() if sample.created_at else None,
                    updated_at=sample.updated_at.isoformat() if sample.updated_at else None
                )
                sample_responses.append(sample_response)

            return DatasetSamplesListResponse(
                samples=sample_responses,
                total_count=total_count,
                generated_dataset_id=generated_dataset_id,
                datagen_event_id=datagen_event_id,
                project_id=project_id
            )

    def get_dataset_sample(self, sample_id: str) -> 'DatasetSampleResponse':
        """
        Get a specific dataset sample by ID.

        Args:
            sample_id: The sample ID

        Returns:
            DatasetSampleResponse: The dataset sample or None if not found
        """
        from .models import DatasetSample, DatasetSampleResponse

        with self.db_session_factory() as session:
            sample = session.query(DatasetSample).filter(DatasetSample.id == sample_id).first()

            if not sample:
                return None

            return DatasetSampleResponse(
                id=str(sample.id),
                generated_dataset_id=str(sample.generated_dataset_id),
                datagen_event_id=str(sample.datagen_event_id) if sample.datagen_event_id else None,
                project_id=str(sample.project_id) if sample.project_id else None,
                job_id=str(sample.job_id) if sample.job_id else None,
                sample_type=sample.sample_type,
                content=sample.content,
                label=sample.label,
                sample_group=sample.sample_group,
                sample_index=sample.sample_index,
                created_at=sample.created_at.isoformat() if sample.created_at else None,
                updated_at=sample.updated_at.isoformat() if sample.updated_at else None
            )
